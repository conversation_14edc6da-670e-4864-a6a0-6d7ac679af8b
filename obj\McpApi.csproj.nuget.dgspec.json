{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\McpApi\\McpApi.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\McpApi\\McpApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\McpApi\\McpApi.csproj", "projectName": "McpApi", "projectPath": "C:\\Users\\<USER>\\Desktop\\McpApi\\McpApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\McpApi\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.17, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[6.0.2, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.411/PortableRuntimeIdentifierGraph.json"}}}}}