import { sqlAgent } from "../agents/sqlAgent";

export const sqlWorkflow = {
  name: "sqlWorkflow",
  description: "SQL Agent ve Executor araçlarını bağlar.",

  async execute(input: { query: string }) {
    console.log(`📝 Kullanıcı sorgusu: ${input.query}`);

    try {
      // Agent'ı çalıştır
      const result = await sqlAgent.generate(input.query);

      console.log(`🤖 Agent yanıtı:`, JSON.stringify(result, null, 2));

      // Eğer agent tool kullandıysa, tool sonuçlarını al
      if (result.toolResults && result.toolResults.length > 0) {
        const toolResult = result.toolResults[0];
        console.log(`🔧 Tool sonucu:`, JSON.stringify(toolResult, null, 2));

        // Tool sonucunu düzgün şekilde parse et
        const toolData = toolResult.result;

        return {
          success: true,
          query: input.query,
          result: {
            text: result.text,
            data: toolData?.data || [],
            count: toolData?.count || 0,
            sqlQuery: toolResult.args?.sql || "Agent tarafından oluşturuldu"
          }
        };
      }
      // Eğer steps içinde tool sonuçları varsa (Gemini'nin yeni formatı)
      else if (result.steps && result.steps.length > 0) {
        const stepsWithTools = result.steps.filter(step => step.toolResults && step.toolResults.length > 0);

        if (stepsWithTools.length > 0) {
          const toolResult = stepsWithTools[0].toolResults[0];
          console.log(`🔧 Step tool sonucu:`, JSON.stringify(toolResult, null, 2));

          return {
            success: true,
            query: input.query,
            result: {
              text: result.text,
              data: toolResult.result?.data || [],
              count: toolResult.result?.count || 0,
              sqlQuery: toolResult.args?.sql || "Agent tarafından oluşturuldu"
            }
          };
        }
      }

      // Sadece metin yanıt varsa - bu durumda agent tool kullanmamış
      console.log(`⚠️ Agent tool kullanmadı, sadece metin yanıt verdi`);
      return {
        success: true,
        query: input.query,
        result: {
          text: result.text + "\n\n(Not: Sorgu çalıştırılmadı, sadece SQL oluşturuldu)",
          data: null,
          count: 0
        }
      };
    } catch (error) {
      console.error(`❌ Workflow hatası:`, error);
      return {
        success: false,
        query: input.query,
        result: {
          text: `Hata oluştu: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`,
          data: null,
          count: 0
        }
      };
    }
  }
};
