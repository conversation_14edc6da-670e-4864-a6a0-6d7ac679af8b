# SQL Chatbot Pro<PERSON><PERSON>u proje, doğal dil sorgularını SQL'e çeviren ve MSSQL veritabanından veri çeken bir chatbot sistemidir. Mastra framework'ü ve Gemini Flash 2.0 AI modeli kullanılarak geliştirilmiştir.

## 🏗️ Proje <PERSON>

```
McpApi/
├── Controllers/           # MCP API Controllers
├── Properties/           # ASP.NET Core ayarları
├── Program.cs           # API ana dosyası
├── appsettings.json     # Konfigürasyon
├── trt-mastra-agent/    # Mastra Agent
│   ├── src/
│   │   ├── mastra/
│   │   │   ├── agents/     # SQL Agent
│   │   │   ├── tools/      # SQL Executor Tool
│   │   │   └── workflows/  # SQL Workflow
│   │   └── server.ts       # Express sunucusu
│   ├── .env             # Environment variables
│   └── package.json     # Node.js dependencies
└── chatbot-ui/          # Web arayüzü
    ├── index.html       # Ana sayfa
    └── script.js        # JavaScript logic
```

## 🚀 Kurulum ve Çalıştırma

### 1. Gereksinimler

- .NET 8.0 SDK
- Node.js 20.9.0+
- MSSQL Server
- Google AI API Key (Gemini Flash 2.0 için)

### 2. Veritabanı Kurulumu

MSSQL'de `sirket` adında bir veritabanı oluşturun ve aşağıdaki tabloyu ekleyin:

```sql
CREATE DATABASE sirket;
USE sirket;

CREATE TABLE personel (
    id INT IDENTITY(1,1) PRIMARY KEY,
    ad NVARCHAR(50) NOT NULL,
    soyad NVARCHAR(50) NOT NULL,
    yas INT NOT NULL,
    maas DECIMAL(10,2) NOT NULL,
    cinsiyet NVARCHAR(10) NOT NULL
);

-- Örnek veriler
INSERT INTO personel (ad, soyad, yas, maas, cinsiyet) VALUES
('Eren', 'Yılkan', 25, 6000, 'Erkek'),
('Ayşe', 'Demir', 28, 5500, 'Kadın'),
('Mehmet', 'Kaya', 35, 7000, 'Erkek'),
('Fatma', 'Çelik', 29, 5200, 'Kadın'),
('Ali', 'Vural', 32, 6500, 'Erkek');
```

### 3. MCP API'yi Başlatma

```bash
cd McpApi
dotnet run
```

API http://localhost:5200 adresinde çalışacak.
Swagger UI: http://localhost:5200

### 4. Mastra Agent'ı Başlatma

```bash
cd trt-mastra-agent
npm install
npm run server:dev
```

Agent http://localhost:3000 adresinde çalışacak.

### 5. Chatbot Arayüzünü Açma

`chatbot-ui/index.html` dosyasını tarayıcıda açın.

## 🔧 Konfigürasyon

### Environment Variables (.env)

```env
GOOGLE_GENERATIVE_AI_API_KEY=your_google_api_key_here
MCP_API_URL=http://localhost:5200
DB_SERVER=EREN
DB_NAME=sirket
```

### Connection String (appsettings.json)

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=EREN;Database=sirket;Trusted_Connection=True;TrustServerCertificate=True;"
  }
}
```

## 📝 Kullanım

### Örnek Sorgular

- "Eren Yılkan kişisinin maaş bilgisini getir"
- "Yaşı 30'dan küçük ve cinsiyeti erkek olan kişilerin tüm bilgilerini getir"
- "Maaşı 5000'den büyük olan personelleri getir"
- "Tüm kadın personellerin ad ve maaş bilgilerini getir"

### API Endpoints

#### MCP API (Port 5200)
- `GET /api/query/health` - Sağlık kontrolü
- `POST /api/query/execute` - SQL sorgusu çalıştırma

#### Mastra Agent (Port 3000)
- `GET /health` - Sağlık kontrolü
- `POST /chat` - Chatbot sorguları
- `POST /test` - Test sorgusu

## 🛠️ Teknolojiler

- **Backend**: ASP.NET Core 8.0, C#
- **AI Framework**: Mastra
- **AI Model**: Google Gemini Flash 2.0
- **Database**: Microsoft SQL Server
- **Frontend**: HTML, CSS, JavaScript
- **Agent Server**: Node.js, Express

## 🔒 Güvenlik

- Sadece SELECT sorguları desteklenir
- SQL injection koruması
- CORS ayarları
- Input validation

## 🐛 Sorun Giderme

### MCP API Bağlantı Hatası
- MSSQL Server'ın çalıştığından emin olun
- Connection string'i kontrol edin
- Port 5200'ün açık olduğunu kontrol edin

### Mastra Agent Hatası
- Google AI API key'in doğru olduğunu kontrol edin
- Node.js sürümünün 20.9.0+ olduğunu kontrol edin
- Port 3000'in açık olduğunu kontrol edin

### Chatbot Arayüz Hatası
- Her iki sunucunun da çalıştığından emin olun
- Browser console'da hata mesajlarını kontrol edin
- CORS ayarlarını kontrol edin

## 📞 Destek

Herhangi bir sorun yaşarsanız, lütfen issue açın veya iletişime geçin.
