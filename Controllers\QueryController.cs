using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using System.Data;
using System.ComponentModel.DataAnnotations;

namespace McpApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class QueryController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<QueryController> _logger;

        public QueryController(IConfiguration configuration, ILogger<QueryController> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        /// <param name="request">SQL sorgu isteği</param>
        /// <returns>Sorgu sonuçları</returns>
        [HttpPost("execute")]
        [ProducesResponseType(typeof(List<Dictionary<string, object>>), 200)]
        [ProducesResponseType(typeof(ErrorResponse), 400)]
        [ProducesResponseType(typeof(ErrorResponse), 500)]
        public async Task<IActionResult> ExecuteQuery([FromBody] QueryRequest request)
        {
            if (request == null || string.IsNullOrWhiteSpace(request.Sql))
            {
                return BadRequest(new ErrorResponse { Error = "SQL sorgusu boş olamaz." });
            }

            var trimmedSql = request.Sql.Trim().ToUpperInvariant();
            if (!trimmedSql.StartsWith("SELECT"))
            {
                return BadRequest(new ErrorResponse { Error = "Sadece SELECT sorguları desteklenmektedir." });
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection")
                ?? "Server=EREN;Database=sirket;Trusted_Connection=True;TrustServerCertificate=True;";

            List<Dictionary<string, object>> resultList = new List<Dictionary<string, object>>();

            try
            {
                _logger.LogInformation("SQL sorgusu çalıştırılıyor: {Sql}", request.Sql);

                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    await conn.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(request.Sql, conn))
                    {
                        cmd.CommandTimeout = 30;

                        using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var row = new Dictionary<string, object>();
                                for (int i = 0; i < reader.FieldCount; i++)
                                {
                                    var value = reader.GetValue(i);
                                    row[reader.GetName(i)] = value == DBNull.Value ? null : value;
                                }
                                resultList.Add(row);
                            }
                        }
                    }
                }

                _logger.LogInformation("Sorgu başarıyla tamamlandı. {Count} kayıt döndürüldü.", resultList.Count);
                return Ok(new QueryResponse { Data = resultList, Count = resultList.Count });
            }
            catch (SqlException ex)
            {
                _logger.LogError(ex, "SQL hatası: {Message}", ex.Message);
                return BadRequest(new ErrorResponse { Error = $"SQL hatası: {ex.Message}" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Beklenmeyen hata: {Message}", ex.Message);
                return StatusCode(500, new ErrorResponse { Error = "Sunucu hatası oluştu." });
            }
        }

        /// <returns>API durum bilgisi</returns>
        [HttpGet("health")]
        [ProducesResponseType(typeof(HealthResponse), 200)]
        public IActionResult Health()
        {
            return Ok(new HealthResponse { Status = "Healthy", Timestamp = DateTime.UtcNow });
        }
    }

    public class QueryRequest
    {

        [Required(ErrorMessage = "SQL sorgusu gereklidir.")]
        [StringLength(5000, ErrorMessage = "SQL sorgusu çok uzun.")]
        public string Sql { get; set; } = string.Empty;
    }

    public class QueryResponse
    {

        public List<Dictionary<string, object>> Data { get; set; } = new();

        public int Count { get; set; }
    }

    public class ErrorResponse
    {

        public string Error { get; set; } = string.Empty;
    }

    public class HealthResponse
    {

        public string Status { get; set; } = string.Empty;

        public DateTime Timestamp { get; set; }
    }
}
