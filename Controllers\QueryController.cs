using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using System.Data;
using System.ComponentModel.DataAnnotations;

namespace McpApi.Controllers
{
    /// <summary>
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class QueryController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<QueryController> _logger;

        public QueryController(IConfiguration configuration, ILogger<QueryController> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        /// <summary>
        /// SQL sorgusu çalıştırır ve sonuçları döndürür
        /// </summary>
        /// <param name="request">SQL sorgu isteği</param>
        /// <returns>Sorgu sonuçları</returns>
        [HttpPost("execute")]
        [ProducesResponseType(typeof(List<Dictionary<string, object>>), 200)]
        [ProducesResponseType(typeof(ErrorResponse), 400)]
        [ProducesResponseType(typeof(ErrorResponse), 500)]
        public async Task<IActionResult> ExecuteQuery([FromBody] QueryRequest request)
        {
            if (request == null || string.IsNullOrWhiteSpace(request.Sql))
            {
                return BadRequest(new ErrorResponse { Error = "SQL sorgusu boş olamaz." });
            }

            // Güvenlik kontrolü - sadece SELECT sorgularına izin ver
            var trimmedSql = request.Sql.Trim().ToUpperInvariant();
            if (!trimmedSql.StartsWith("SELECT"))
            {
                return BadRequest(new ErrorResponse { Error = "Sadece SELECT sorguları desteklenmektedir." });
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection")
                ?? "Server=EREN;Database=sirket;Trusted_Connection=True;TrustServerCertificate=True;";

            List<Dictionary<string, object>> resultList = new List<Dictionary<string, object>>();

            try
            {
                _logger.LogInformation("SQL sorgusu çalıştırılıyor: {Sql}", request.Sql);

                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    await conn.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(request.Sql, conn))
                    {
                        // Timeout ayarla
                        cmd.CommandTimeout = 30;

                        using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var row = new Dictionary<string, object>();
                                for (int i = 0; i < reader.FieldCount; i++)
                                {
                                    var value = reader.GetValue(i);
                                    row[reader.GetName(i)] = value == DBNull.Value ? null : value;
                                }
                                resultList.Add(row);
                            }
                        }
                    }
                }

                _logger.LogInformation("Sorgu başarıyla tamamlandı. {Count} kayıt döndürüldü.", resultList.Count);
                return Ok(new QueryResponse { Data = resultList, Count = resultList.Count });
            }
            catch (SqlException ex)
            {
                _logger.LogError(ex, "SQL hatası: {Message}", ex.Message);
                return BadRequest(new ErrorResponse { Error = $"SQL hatası: {ex.Message}" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Beklenmeyen hata: {Message}", ex.Message);
                return StatusCode(500, new ErrorResponse { Error = "Sunucu hatası oluştu." });
            }
        }

        /// <summary>
        /// API durumunu kontrol eder
        /// </summary>
        /// <returns>API durum bilgisi</returns>
        [HttpGet("health")]
        [ProducesResponseType(typeof(HealthResponse), 200)]
        public IActionResult Health()
        {
            return Ok(new HealthResponse { Status = "Healthy", Timestamp = DateTime.UtcNow });
        }
    }

    /// <summary>
    /// SQL sorgu isteği modeli
    /// </summary>
    public class QueryRequest
    {
        /// <summary>
        /// Çalıştırılacak SQL sorgusu
        /// </summary>
        [Required(ErrorMessage = "SQL sorgusu gereklidir.")]
        [StringLength(5000, ErrorMessage = "SQL sorgusu çok uzun.")]
        public string Sql { get; set; } = string.Empty;
    }

    /// <summary>
    /// Sorgu yanıt modeli
    /// </summary>
    public class QueryResponse
    {
        /// <summary>
        /// Sorgu sonuç verileri
        /// </summary>
        public List<Dictionary<string, object>> Data { get; set; } = new();

        /// <summary>
        /// Döndürülen kayıt sayısı
        /// </summary>
        public int Count { get; set; }
    }

    /// <summary>
    /// Hata yanıt modeli
    /// </summary>
    public class ErrorResponse
    {
        /// <summary>
        /// Hata mesajı
        /// </summary>
        public string Error { get; set; } = string.Empty;
    }

    /// <summary>
    /// Sağlık durumu yanıt modeli
    /// </summary>
    public class HealthResponse
    {
        /// <summary>
        /// API durumu
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Zaman damgası
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
}
