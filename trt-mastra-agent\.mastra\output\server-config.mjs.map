{"version": 3, "file": "server-config.mjs", "sources": ["../../src/mastra/index.ts"], "sourcesContent": ["import { <PERSON>stra } from '@mastra/core';\nimport { sqlAgent } from './agents/sqlAgent';\nimport dotenv from \"dotenv\";\n\n// Environment variables'ları yükle\ndotenv.config();\n\n// Mastra instance'ını oluştur ve export et\nexport const mastra = new Mastra({\n  agents: {\n    sqlAgent: sqlAgent\n  }\n});\n"], "names": [], "mappings": "AAEO,MAAA,SAAY;;;;"}