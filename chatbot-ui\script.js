class SQLChatbot {
    constructor() {
        this.chatMessages = document.getElementById('chatMessages');
        this.chatForm = document.getElementById('chatForm');
        this.chatInput = document.getElementById('chatInput');
        this.sendButton = document.getElementById('sendButton');
        
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        this.chatForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.sendMessage();
        });

        this.chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
    }

    async sendMessage() {
        const message = this.chatInput.value.trim();
        if (!message) return;

        this.addMessage(message, 'user');
        this.chatInput.value = '';
        this.setLoading(true);

        try {
            const response = await this.queryAgent(message);
            this.addBotResponse(response);
        } catch (error) {
            console.error('Hata:', error);
            this.addMessage(`Üzgünüm, bir hata oluştu: ${error.message}`, 'bot');
        } finally {
            this.setLoading(false);
        }
    }

    async queryAgent(query) {
        // Mastra Agent'a sorgu gönder
        console.log('Mastra Agent\'a sorgu gönderiliyor:', query);

        const response = await fetch('http://localhost:3000/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ query: query })
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('HTTP Error:', response.status, errorText);
            throw new Error(`Sunucu hatası (${response.status}): ${errorText}`);
        }

        const result = await response.json();
        console.log('Agent yanıtı:', result);

        // Mastra Agent'ın yanıt formatını işle
        if (result.success && result.result) {
            const agentResult = result.result;

            // Eğer agent tool sonuçları döndürdüyse
            if (agentResult.data && Array.isArray(agentResult.data)) {
                return {
                    originalQuery: query,
                    sqlQuery: agentResult.sqlQuery || 'Agent tarafından oluşturuldu',
                    data: agentResult.data,
                    count: agentResult.count || agentResult.data.length
                };
            }
            // Eğer sadece metin yanıt varsa
            else if (agentResult.text) {
                return {
                    originalQuery: query,
                    textResponse: agentResult.text,
                    data: null,
                    count: 0
                };
            }
            // Diğer durumlar için debug bilgisi
            else {
                console.log('Beklenmeyen agent yanıt formatı:', agentResult);
                return {
                    originalQuery: query,
                    textResponse: `Agent yanıtı: ${JSON.stringify(agentResult, null, 2)}`,
                    data: null,
                    count: 0
                };
            }
        } else {
            console.error('Agent yanıt hatası:', result);
            throw new Error(`Agent hatası: ${result.error || JSON.stringify(result)}`);
        }
    }

    convertToSQL(query) {
        const lowerQuery = query.toLowerCase();
        let sql = "SELECT ";
        let columns = "*";
        let conditions = [];
        if (lowerQuery.includes('maaş') && !lowerQuery.includes('tüm')) {
            columns = "ad, soyad, maas";
        } else if (lowerQuery.includes('ad') && lowerQuery.includes('soyad')) {
            columns = "ad, soyad";
        } else if (lowerQuery.includes('yaş')) {
            columns = "ad, soyad, DATEDIFF(YEAR, dogum_tarihi, GETDATE()) as yas";
        }

        sql += columns + " FROM personel";
        if (lowerQuery.includes('erkek')) {
            conditions.push("cinsiyet = 'E'");
        }
        if (lowerQuery.includes('kadın')) {
            conditions.push("cinsiyet = 'K'");
        }
        const ageMatch = lowerQuery.match(/yaşı?\s*(\d+)\s*(?:dan|den)\s*(küçük|büyük)/);
        if (ageMatch) {
            const age = ageMatch[1];
            const operator = ageMatch[2] === 'küçük' ? '<' : '>';
            conditions.push(`DATEDIFF(YEAR, dogum_tarihi, GETDATE()) ${operator} ${age}`);
        }
        const salaryMatch = lowerQuery.match(/maaşı?\s*(\d+)\s*(?:dan|den)\s*(küçük|büyük)/);
        if (salaryMatch) {
            const salary = salaryMatch[1];
            const operator = salaryMatch[2] === 'küçük' ? '<' : '>';
            conditions.push(`maas ${operator} ${salary}`);
        }
        const nameMatch = lowerQuery.match(/(\w+)(?:\s+(\w+))?/);
        if (nameMatch && (lowerQuery.includes('getir') || lowerQuery.includes('bilgi'))) {
            const firstName = nameMatch[1];
            if (firstName !== 'yaşı' && firstName !== 'maaşı') {
                conditions.push(`ad LIKE '%${firstName}%'`);
                if (nameMatch[2]) {
                    conditions.push(`soyad LIKE '%${nameMatch[2]}%'`);
                }
            }
        }

        if (conditions.length > 0) {
            sql += " WHERE " + conditions.join(" AND ");
        }

        return sql;
    }

    addMessage(message, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.textContent = message;
        
        const timeDiv = document.createElement('div');
        timeDiv.className = 'message-time';
        timeDiv.textContent = new Date().toLocaleTimeString('tr-TR');
        
        messageDiv.appendChild(contentDiv);
        contentDiv.appendChild(timeDiv);
        
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    addBotResponse(response) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message bot';

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';

        let content = `<strong>Sorgunuz:</strong> ${response.originalQuery}<br>`;
        if (response.sqlQuery && response.sqlQuery !== 'Agent tarafından oluşturuldu') {
            content += `<strong>SQL:</strong> <code>${response.sqlQuery}</code><br><br>`;
        }
        if (response.textResponse) {
            content += `<strong>Agent Yanıtı:</strong><br>${response.textResponse}`;
        }
        else if (response.data && response.data.length > 0) {
            content += `<strong>Sonuçlar (${response.count} kayıt):</strong>`;
            content += this.createTable(response.data);
        } else {
            content += '<strong>Sonuç:</strong> Hiç kayıt bulunamadı.';
        }

        contentDiv.innerHTML = content;

        const timeDiv = document.createElement('div');
        timeDiv.className = 'message-time';
        timeDiv.textContent = new Date().toLocaleTimeString('tr-TR');

        messageDiv.appendChild(contentDiv);
        contentDiv.appendChild(timeDiv);

        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }
    createTable(data) {
        if (!data || data.length === 0) return '';
        
        const headers = Object.keys(data[0]);
        let table = '<table class="result-table"><thead><tr>';
        
        headers.forEach(header => {
            table += `<th>${header}</th>`;
        });
        table += '</tr></thead><tbody>';
        
        data.forEach(row => {
            table += '<tr>';
            headers.forEach(header => {
                const value = row[header];
                table += `<td>${value !== null ? value : '-'}</td>`;
            });
            table += '</tr>';
        });
        
        table += '</tbody></table>';
        return table;
    }
    setLoading(isLoading) {
        if (isLoading) {
            this.sendButton.disabled = true;
            this.sendButton.innerHTML = '<span class="loading">Düşünüyor<span class="loading-dots"></span></span>';
            this.chatInput.disabled = true;
        } else {
            this.sendButton.disabled = false;
            this.sendButton.textContent = 'Gönder';
            this.chatInput.disabled = false;
            this.chatInput.focus();
        }
    }
    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }
}
function setQuery(query) {
    document.getElementById('chatInput').value = query;
    document.getElementById('chatInput').focus();
}
document.addEventListener('DOMContentLoaded', () => {
    new SQLChatbot();
});
