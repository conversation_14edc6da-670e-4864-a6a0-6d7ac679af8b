import express, { Request<PERSON>and<PERSON> } from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { sqlWorkflow } from './mastra/workflows/sqlWorkflow';
import { mastra } from '../mastra.config';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

app.use(cors());
app.use(express.json());

const healthHandler: RequestHandler = (_req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'Mastra SQL Agent'
    });
};

app.get('/health', healthHandler);

const chatHandler: RequestHandler = async (req, res) => {
    try {
        const { query } = req.body;

        if (!query) {
            res.status(400).json({
                error: 'Sorgu gereklidir'
            });
            return;
        }

        console.log(`📝 Kullanıcı sorgusu: ${query}`);

        const result = await sqlWorkflow.execute({ query });

        console.log(`✅ Workflow sonucu:`, JSON.stringify(result, null, 2));

        res.json({
            success: result.success,
            query: query,
            result: result.result
        });

    } catch (error) {
        console.error('❌ Chat endpoint hatası:', error);
        console.error('Hata detayı:', error instanceof Error ? error.stack : error);
        res.status(500).json({
            error: error instanceof Error ? error.message : 'Bilinmeyen hata'
        });
        return;
    }
};

app.post('/chat', chatHandler);

// Agent'ı doğrudan test etmek için endpoint
const agentHandler: RequestHandler = async (req, res) => {
    try {
        const { query } = req.body;

        if (!query) {
            res.status(400).json({
                error: 'Sorgu gereklidir'
            });
            return;
        }

        console.log(`🤖 Agent'a gönderilen sorgu: ${query}`);

        // Agent'ı al ve çalıştır
        const agent = mastra.getAgent('sqlAgent');
        const result = await agent.generate(query);

        console.log(`✅ Agent sonucu:`, JSON.stringify(result, null, 2));

        res.json({
            success: true,
            query: query,
            result: result.text,
            toolCalls: result.toolCalls || [],
            usage: result.usage
        });

    } catch (error) {
        console.error('❌ Agent endpoint hatası:', error);
        console.error('Hata detayı:', error instanceof Error ? error.stack : error);
        res.status(500).json({
            error: error instanceof Error ? error.message : 'Bilinmeyen hata'
        });
        return;
    }
};

app.post('/agent', agentHandler);

app.listen(PORT, () => {
    console.log(`🚀 Mastra SQL Agent sunucusu http://localhost:${PORT} adresinde çalışıyor`);
    console.log(`📋 Endpoints:`);
    console.log(`   GET  /health - Sağlık kontrolü`);
    console.log(`   POST /chat   - Chatbot sorguları (workflow ile)`);
    console.log(`   POST /agent  - Agent'ı doğrudan test et`);
    console.log(`\n💡 Kullanım örnekleri:`);
    console.log(`   curl -X POST http://localhost:${PORT}/chat -H "Content-Type: application/json" -d '{"query":"Yaşı 30 dan küçük erkekleri getir"}'`);
    console.log(`   curl -X POST http://localhost:${PORT}/agent -H "Content-Type: application/json" -d '{"query":"Ahmet adlı personelleri getir"}'`);
});

process.on('SIGTERM', () => {
    console.log('🛑 Sunucu kapatılıyor...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('🛑 Sunucu kapatılıyor...');
    process.exit(0);
});
