import express, { Request<PERSON><PERSON><PERSON> } from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { sqlWorkflow } from './mastra/workflows/sqlWorkflow';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

app.use(cors());
app.use(express.json());

const healthHandler: RequestHandler = (_req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'Mastra SQL Agent'
    });
};

app.get('/health', healthHandler);

const chatHandler: RequestHandler = async (req, res) => {
    try {
        const { query } = req.body;

        if (!query) {
            res.status(400).json({
                error: 'Sorgu gereklidir'
            });
            return;
        }

        console.log(`📝 Kullanıcı sorgusu: ${query}`);

        const result = await sqlWorkflow.execute({ query });

        console.log(`✅ Workflow sonucu:`, JSON.stringify(result, null, 2));

        res.json({
            success: result.success,
            query: query,
            result: result.result
        });

    } catch (error) {
        console.error('❌ Chat endpoint hatası:', error);
        console.error('Hata detayı:', error instanceof Error ? error.stack : error);
        res.status(500).json({
            error: error instanceof Error ? error.message : 'Bilinmeyen hata'
        });
        return;
    }
};

app.post('/chat', chatHandler);

app.listen(PORT, () => {
    console.log(`🚀 Mastra SQL Agent sunucusu http://localhost:${PORT} adresinde çalışıyor`);
    console.log(`📋 Endpoints:`);
    console.log(`   GET  /health - Sağlık kontrolü`);
    console.log(`   POST /chat   - Chatbot sorguları`);
    console.log(`\n💡 Kullanım örneği:`);
    console.log(`   curl -X POST http://localhost:${PORT}/chat -H "Content-Type: application/json" -d '{"query":"Yaşı 30 dan küçük erkekleri getir"}'`);
});

process.on('SIGTERM', () => {
    console.log('🛑 Sunucu kapatılıyor...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('🛑 Sunucu kapatılıyor...');
    process.exit(0);
});
