import { Tool } from "@mastra/core/tools";
import { z } from "zod";

export const sqlExecutor = new Tool({
  id: "sqlExecutor",
  description: "MCP API üzerinden SQL sorgusu çalıştırır ve sonuçları döndürür.",
  inputSchema: z.object({
    sql: z.string().describe("Çalıştırılacak SQL sorgusu"),
  }),
  outputSchema: z.object({
    data: z.array(z.record(z.any())).describe("Sorgu sonuçları"),
    count: z.number().describe("Döndürülen kayıt sayısı"),
  }),
  execute: async ({ context }) => {
    const { sql } = context;
    const mcpApiUrl = process.env.MCP_API_URL || "http://localhost:5200";

    try {
      console.log(`SQL sorgusu çalıştırılıyor: ${sql}`);

      const response = await fetch(`${mcpApiUrl}/api/query/execute`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json"
        },
        body: JSON.stringify({ sql }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`MCP API hata (${response.status}): ${errorText}`);
      }

      const result = await response.json();
      console.log(`Sorgu başarılı: ${result.count} kayıt döndürüldü`);

      return {
        data: result.data || result, // Eski API uyumluluğu için
        count: result.count || (Array.isArray(result.data) ? result.data.length : 0)
      };
    } catch (error) {
      console.error("SQL Executor hatası:", error);
      throw new Error(`SQL sorgusu çalıştırılamadı: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`);
    }
  },
});
