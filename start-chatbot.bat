@echo off
echo 🚀 SQL Chatbot Sistemi Başlatılıyor...
echo.

echo 📋 1. MCP API başlatılıyor...
start "MCP API" cmd /k "cd /d %~dp0 && dotnet run"
timeout /t 5 /nobreak >nul

echo 📋 2. Mastra Agent başlatılıyor...
start "Mastra Agent" cmd /k "cd /d %~dp0trt-mastra-agent && npm run server:dev"
timeout /t 5 /nobreak >nul

echo 📋 3. Chatbot arayüzü açılıyor...
start "" "%~dp0chatbot-ui\index.html"

echo.
echo ✅ Tüm servisler başlatıldı!
echo.
echo 📍 Açık Servisler:
echo    - MCP API: http://localhost:5200
echo    - Mastra Agent: http://localhost:3000  
echo    - Chatbot: Tarayıcıda açıldı
echo    - Mastra Playground: http://localhost:4111 (opsiyonel)
echo.
echo 💡 Chatbot'u kullanmaya başlayabilirsiniz!
echo.
pause
