{"version": "2.0.0", "tasks": [{"label": "Start MCP API", "type": "shell", "command": "dotnet", "args": ["run"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Start Mastra Agent", "type": "shell", "command": "npm", "args": ["run", "server:dev"], "options": {"cwd": "${workspaceFolder}/trt-mastra-agent"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Start All Services", "dependsOrder": "parallel", "dependsOn": ["Start MCP API", "Start Mastra Agent"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}}]}